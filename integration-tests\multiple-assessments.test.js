const TestHelpers = require('./utils/test-helpers');
const fs = require('fs');
const path = require('path');

describe('ATMA Multiple Assessments Test', () => {
  let testHelpers;
  let assessmentVariants;

  beforeAll(async () => {
    testHelpers = new TestHelpers();
    
    // Load assessment variants
    const variantsPath = path.join(__dirname, 'test-data', 'assessment-variants.json');
    const variantsData = fs.readFileSync(variantsPath, 'utf8');
    assessmentVariants = JSON.parse(variantsData);
    
    console.log('🚀 Starting ATMA Multiple Assessments Test');
    console.log(`📊 Testing ${Object.keys(assessmentVariants).length} assessment variants`);
  });

  describe('Multiple Assessment Variants', () => {
    const testResults = [];

    Object.entries(assessmentVariants).forEach(([variantName, assessmentData]) => {
      it(`should process ${variantName} assessment successfully`, async () => {
        console.log(`\n🧪 Testing variant: ${variantName}`);
        
        // Register and login user for this variant
        const testUser = await testHelpers.registerUser();
        const loginResult = await testHelpers.loginUser(testUser.email, testUser.password);
        const authToken = loginResult.token;
        
        console.log(`👤 User: ${testUser.email}`);
        
        // Submit assessment
        const submitResult = await testHelpers.submitAssessment(authToken, assessmentData);
        const jobId = submitResult.data.jobId;
        
        console.log(`📝 Assessment submitted: ${jobId}`);
        
        // Wait for processing (with shorter timeout for multiple tests)
        const completionResult = await testHelpers.waitForAssessmentCompletion(
          authToken, 
          jobId, 
          90000 // 90 seconds timeout
        );
        
        expect(completionResult.data).toHaveProperty('status', 'completed');
        console.log(`✅ Processing completed for ${variantName}`);
        
        // Get results from archive
        await global.sleep(3000); // Wait for archive save
        const archiveResults = await testHelpers.getAnalysisResults(authToken);
        
        expect(archiveResults.data.length).toBeGreaterThan(0);
        
        const analysisResult = archiveResults.data.find(result => 
          result.user_id === testUser.response.data.user.id
        );
        
        expect(analysisResult).toBeDefined();
        testHelpers.verifyAssessmentResult(analysisResult);
        
        // Store result for summary
        testResults.push({
          variant: variantName,
          userId: testUser.response.data.user.id,
          email: testUser.email,
          jobId: jobId,
          resultId: analysisResult.id,
          archetype: analysisResult.persona_profile.archetype,
          createdAt: analysisResult.created_at
        });
        
        console.log(`🎭 ${variantName} -> ${analysisResult.persona_profile.archetype}`);
        
      }, 120000); // 2 minutes timeout per test
    });

    afterAll(() => {
      console.log('\n📊 MULTIPLE ASSESSMENTS TEST SUMMARY');
      console.log('=' .repeat(80));
      
      testResults.forEach((result, index) => {
        console.log(`${index + 1}. ${result.variant}`);
        console.log(`   👤 User: ${result.email}`);
        console.log(`   🎭 Archetype: ${result.archetype}`);
        console.log(`   🆔 Job ID: ${result.jobId}`);
        console.log(`   📋 Result ID: ${result.resultId}`);
        console.log(`   📅 Created: ${result.createdAt}`);
        console.log('');
      });
      
      console.log('=' .repeat(80));
      console.log(`✅ Successfully processed ${testResults.length} assessment variants`);
      
      // Check for archetype diversity
      const archetypes = testResults.map(r => r.archetype);
      const uniqueArchetypes = [...new Set(archetypes)];
      
      console.log(`🎭 Generated ${uniqueArchetypes.length} unique archetypes:`);
      uniqueArchetypes.forEach(archetype => {
        const count = archetypes.filter(a => a === archetype).length;
        console.log(`   - ${archetype} (${count}x)`);
      });
    });
  });

  describe('Assessment Data Validation', () => {
    it('should validate all assessment variants have correct structure', () => {
      Object.entries(assessmentVariants).forEach(([variantName, assessmentData]) => {
        console.log(`🔍 Validating ${variantName} structure...`);
        
        // Check required top-level properties
        expect(assessmentData).toHaveProperty('riasec');
        expect(assessmentData).toHaveProperty('ocean');
        expect(assessmentData).toHaveProperty('viaIs');
        
        // Check RIASEC properties
        const riasecKeys = ['realistic', 'investigative', 'artistic', 'social', 'enterprising', 'conventional'];
        riasecKeys.forEach(key => {
          expect(assessmentData.riasec).toHaveProperty(key);
          expect(assessmentData.riasec[key]).toBeGreaterThanOrEqual(0);
          expect(assessmentData.riasec[key]).toBeLessThanOrEqual(100);
        });
        
        // Check OCEAN properties
        const oceanKeys = ['openness', 'conscientiousness', 'extraversion', 'agreeableness', 'neuroticism'];
        oceanKeys.forEach(key => {
          expect(assessmentData.ocean).toHaveProperty(key);
          expect(assessmentData.ocean[key]).toBeGreaterThanOrEqual(0);
          expect(assessmentData.ocean[key]).toBeLessThanOrEqual(100);
        });
        
        // Check VIA-IS properties (sample of required ones)
        const viaIsKeys = ['creativity', 'curiosity', 'judgment', 'loveOfLearning', 'perspective'];
        viaIsKeys.forEach(key => {
          expect(assessmentData.viaIs).toHaveProperty(key);
          expect(assessmentData.viaIs[key]).toBeGreaterThanOrEqual(0);
          expect(assessmentData.viaIs[key]).toBeLessThanOrEqual(100);
        });
        
        console.log(`✅ ${variantName} structure validated`);
      });
    });

    it('should have diverse assessment profiles', () => {
      const variants = Object.entries(assessmentVariants);
      
      // Check that we have different dominant RIASEC types
      const dominantRiasec = variants.map(([name, data]) => {
        const riasec = data.riasec;
        const maxKey = Object.keys(riasec).reduce((a, b) => riasec[a] > riasec[b] ? a : b);
        return { name, dominant: maxKey, score: riasec[maxKey] };
      });
      
      console.log('🎯 Dominant RIASEC types:');
      dominantRiasec.forEach(({ name, dominant, score }) => {
        console.log(`   ${name}: ${dominant} (${score})`);
      });
      
      // Should have at least 3 different dominant types
      const uniqueDominant = [...new Set(dominantRiasec.map(d => d.dominant))];
      expect(uniqueDominant.length).toBeGreaterThanOrEqual(3);
      
      console.log(`✅ Found ${uniqueDominant.length} different dominant RIASEC types`);
    });
  });
});
