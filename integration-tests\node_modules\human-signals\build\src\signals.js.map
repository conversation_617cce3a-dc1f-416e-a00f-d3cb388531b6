{"version": 3, "sources": ["../../src/signals.js"], "names": ["getSignals", "realtimeSignals", "signals", "SIGNALS", "map", "normalizeSignal", "name", "number", "defaultNumber", "description", "action", "forced", "standard", "constantSignal", "constants", "supported", "undefined"], "mappings": "gGAAA;;AAEA;AACA;;;;AAIO,KAAMA,CAAAA,UAAU,CAAG,UAAW;AACnC,KAAMC,CAAAA,eAAe,CAAG,kCAAxB;AACA,KAAMC,CAAAA,OAAO,CAAG,CAAC,GAAGC,aAAJ,CAAa,GAAGF,eAAhB,EAAiCG,GAAjC,CAAqCC,eAArC,CAAhB;AACA,MAAOH,CAAAA,OAAP;AACD,CAJM,C;;;;;;;;AAYP,KAAMG,CAAAA,eAAe,CAAG,SAAS;AAC/BC,IAD+B;AAE/BC,MAAM,CAAEC,aAFuB;AAG/BC,WAH+B;AAI/BC,MAJ+B;AAK/BC,MAAM,CAAG,KALsB;AAM/BC,QAN+B,CAAT;AAOrB;AACD,KAAM;AACJV,OAAO,CAAE,CAAE,CAACI,IAAD,EAAQO,cAAV,CADL;AAEFC,aAFJ;AAGA,KAAMC,CAAAA,SAAS,CAAGF,cAAc,GAAKG,SAArC;AACA,KAAMT,CAAAA,MAAM,CAAGQ,SAAS,CAAGF,cAAH,CAAoBL,aAA5C;AACA,MAAO,CAAEF,IAAF,CAAQC,MAAR,CAAgBE,WAAhB,CAA6BM,SAA7B,CAAwCL,MAAxC,CAAgDC,MAAhD,CAAwDC,QAAxD,CAAP;AACD,CAdD", "sourcesContent": ["import { constants } from 'os'\n\nimport { <PERSON><PERSON><PERSON><PERSON> } from './core.js'\nimport { getRealtimeSignals } from './realtime.js'\n\n// Retrieve list of know signals (including realtime) with information about\n// them\nexport const getSignals = function() {\n  const realtimeSignals = getRealtimeSignals()\n  const signals = [...SIGNALS, ...realtimeSignals].map(normalizeSignal)\n  return signals\n}\n\n// Normalize signal:\n//  - `number`: signal numbers are OS-specific. This is taken into account by\n//    `os.constants.signals`. However we provide a default `number` since some\n//     signals are not defined for some OS.\n//  - `forced`: set default to `false`\n//  - `supported`: set value\nconst normalizeSignal = function({\n  name,\n  number: defaultNumber,\n  description,\n  action,\n  forced = false,\n  standard,\n}) {\n  const {\n    signals: { [name]: constantSignal },\n  } = constants\n  const supported = constantSignal !== undefined\n  const number = supported ? constantSignal : defaultNumber\n  return { name, number, description, supported, action, forced, standard }\n}\n"], "file": "src/signals.js"}