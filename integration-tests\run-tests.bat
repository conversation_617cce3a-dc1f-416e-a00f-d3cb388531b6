@echo off
setlocal enabledelayedexpansion

echo.
echo ========================================
echo    ATMA Integration Tests Runner
echo ========================================
echo.

if "%1"=="" (
    echo Usage: run-tests.bat [command]
    echo.
    echo Available commands:
    echo   full-flow     - Run complete end-to-end test
    echo   individual    - Run individual component tests
    echo   jest          - Run all Jest tests
    echo   health        - Check service health
    echo   help          - Show this help message
    echo.
    echo Examples:
    echo   run-tests.bat full-flow
    echo   run-tests.bat individual
    echo   run-tests.bat jest
    echo.
    goto :end
)

if "%1"=="help" (
    echo Usage: run-tests.bat [command]
    echo.
    echo Available commands:
    echo   full-flow     - Run complete end-to-end test
    echo   individual    - Run individual component tests
    echo   jest          - Run all Jest tests
    echo   health        - Check service health
    echo   help          - Show this help message
    echo.
    goto :end
)

if "%1"=="full-flow" (
    echo Running Full Flow Integration Test...
    echo This test will run the complete end-to-end flow:
    echo 1. Register user
    echo 2. Login user
    echo 3. Submit assessment
    echo 4. Wait for processing
    echo 5. Verify results in archive
    echo.
    echo This may take 2-5 minutes...
    echo.
    node full-flow-test.js
    goto :end
)

if "%1"=="individual" (
    echo Running Individual Component Tests...
    echo.
    npx jest individual-tests.test.js --verbose
    goto :end
)

if "%1"=="jest" (
    echo Running All Jest Tests...
    echo.
    npx jest --verbose
    goto :end
)

if "%1"=="health" (
    echo Running Service Health Check...
    echo.
    node run-tests.js health
    goto :end
)

echo Unknown command: %1
echo Use "run-tests.bat help" for usage information.

:end
echo.
echo ========================================
echo           Test Run Complete
echo ========================================
pause
