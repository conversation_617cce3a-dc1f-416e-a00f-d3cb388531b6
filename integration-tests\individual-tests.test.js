const TestHelpers = require('./utils/test-helpers');

describe('ATMA Individual Component Tests', () => {
  let testHelpers;

  beforeAll(async () => {
    testHelpers = new TestHelpers();
    console.log('🚀 Starting ATMA Individual Component Tests');
  });

  describe('Auth Service Tests', () => {
    let testUser;

    it('should register a new user', async () => {
      testUser = await testHelpers.registerUser();
      
      expect(testUser.response).toHaveProperty('success', true);
      expect(testUser.response.data.user).toHaveProperty('id');
      expect(testUser.response.data.user).toHaveProperty('email', testUser.email);
      expect(testUser.response.data.user).toHaveProperty('tokenBalance');
      
      console.log(`✅ User registered: ${testUser.email}`);
    });

    it('should login with correct credentials', async () => {
      const loginResult = await testHelpers.loginUser(testUser.email, testUser.password);
      
      expect(loginResult.response).toHaveProperty('success', true);
      expect(loginResult.response.data).toHaveProperty('token');
      expect(loginResult.response.data).toHaveProperty('user');
      
      console.log(`✅ Login successful`);
    });

    it('should reject login with wrong password', async () => {
      try {
        await testHelpers.loginUser(testUser.email, 'wrongpassword');
        fail('Should have thrown an error');
      } catch (error) {
        expect(error.response.status).toBe(401);
        expect(error.response.data.success).toBe(false);
        console.log(`✅ Wrong password correctly rejected`);
      }
    });

    it('should reject login with non-existent email', async () => {
      try {
        await testHelpers.loginUser('<EMAIL>', testUser.password);
        fail('Should have thrown an error');
      } catch (error) {
        expect(error.response.status).toBe(401);
        expect(error.response.data.success).toBe(false);
        console.log(`✅ Non-existent email correctly rejected`);
      }
    });
  });

  describe('Assessment Service Tests', () => {
    let testUser, authToken;

    beforeAll(async () => {
      testUser = await testHelpers.registerUser();
      const loginResult = await testHelpers.loginUser(testUser.email, testUser.password);
      authToken = loginResult.token;
    });

    it('should submit valid assessment data', async () => {
      const assessmentData = testHelpers.loadSampleAssessment();
      const submitResult = await testHelpers.submitAssessment(authToken, assessmentData);
      
      expect(submitResult).toHaveProperty('success', true);
      expect(submitResult.data).toHaveProperty('jobId');
      expect(submitResult.data).toHaveProperty('status', 'queued');
      
      console.log(`✅ Assessment submitted: ${submitResult.data.jobId}`);
    });

    it('should reject assessment without authentication', async () => {
      const assessmentData = testHelpers.loadSampleAssessment();
      
      try {
        testHelpers.assessmentClient.removeAuthToken();
        await testHelpers.assessmentClient.post('/assessments/submit', assessmentData);
        fail('Should have thrown an error');
      } catch (error) {
        expect(error.response.status).toBe(401);
        expect(error.response.data.success).toBe(false);
        console.log(`✅ Unauthenticated request correctly rejected`);
      }
    });

    it('should reject invalid assessment data', async () => {
      const invalidData = {
        riasec: { realistic: 'invalid' }, // Should be number
        ocean: {}, // Missing required fields
        viaIs: {} // Missing required fields
      };

      try {
        await testHelpers.submitAssessment(authToken, invalidData);
        fail('Should have thrown an error');
      } catch (error) {
        expect(error.response.status).toBe(400);
        expect(error.response.data.success).toBe(false);
        console.log(`✅ Invalid assessment data correctly rejected`);
      }
    });

    it('should check assessment status', async () => {
      const assessmentData = testHelpers.loadSampleAssessment();
      const submitResult = await testHelpers.submitAssessment(authToken, assessmentData);
      const jobId = submitResult.data.jobId;
      
      const statusResult = await testHelpers.checkAssessmentStatus(authToken, jobId);
      
      expect(statusResult).toHaveProperty('success', true);
      expect(statusResult.data).toHaveProperty('status');
      expect(['queued', 'processing', 'completed', 'failed']).toContain(statusResult.data.status);
      
      console.log(`✅ Status check successful: ${statusResult.data.status}`);
    });
  });

  describe('Archive Service Tests', () => {
    let testUser, authToken;

    beforeAll(async () => {
      testUser = await testHelpers.registerUser();
      const loginResult = await testHelpers.loginUser(testUser.email, testUser.password);
      authToken = loginResult.token;
    });

    it('should retrieve empty results for new user', async () => {
      const results = await testHelpers.getAnalysisResults(authToken);
      
      expect(results).toHaveProperty('success', true);
      expect(results.data).toBeInstanceOf(Array);
      // New user should have no results initially
      expect(results.data.length).toBe(0);
      
      console.log(`✅ Empty results retrieved for new user`);
    });

    it('should reject requests without authentication', async () => {
      try {
        testHelpers.archiveClient.removeAuthToken();
        await testHelpers.archiveClient.get('/archive/results');
        fail('Should have thrown an error');
      } catch (error) {
        expect(error.response.status).toBe(401);
        expect(error.response.data.success).toBe(false);
        console.log(`✅ Unauthenticated archive request correctly rejected`);
      }
    });
  });

  describe('Data Validation Tests', () => {
    it('should validate sample assessment data structure', () => {
      const assessmentData = testHelpers.loadSampleAssessment();
      
      // Validate RIASEC
      expect(assessmentData).toHaveProperty('riasec');
      expect(assessmentData.riasec).toHaveProperty('realistic');
      expect(assessmentData.riasec).toHaveProperty('investigative');
      expect(assessmentData.riasec).toHaveProperty('artistic');
      expect(assessmentData.riasec).toHaveProperty('social');
      expect(assessmentData.riasec).toHaveProperty('enterprising');
      expect(assessmentData.riasec).toHaveProperty('conventional');
      
      // Validate OCEAN
      expect(assessmentData).toHaveProperty('ocean');
      expect(assessmentData.ocean).toHaveProperty('openness');
      expect(assessmentData.ocean).toHaveProperty('conscientiousness');
      expect(assessmentData.ocean).toHaveProperty('extraversion');
      expect(assessmentData.ocean).toHaveProperty('agreeableness');
      expect(assessmentData.ocean).toHaveProperty('neuroticism');
      
      // Validate VIA-IS
      expect(assessmentData).toHaveProperty('viaIs');
      expect(assessmentData.viaIs).toHaveProperty('creativity');
      expect(assessmentData.viaIs).toHaveProperty('curiosity');
      expect(assessmentData.viaIs).toHaveProperty('judgment');
      
      // Validate value ranges (should be 0-100)
      Object.values(assessmentData.riasec).forEach(value => {
        expect(value).toBeGreaterThanOrEqual(0);
        expect(value).toBeLessThanOrEqual(100);
      });
      
      Object.values(assessmentData.ocean).forEach(value => {
        expect(value).toBeGreaterThanOrEqual(0);
        expect(value).toBeLessThanOrEqual(100);
      });
      
      Object.values(assessmentData.viaIs).forEach(value => {
        expect(value).toBeGreaterThanOrEqual(0);
        expect(value).toBeLessThanOrEqual(100);
      });
      
      console.log(`✅ Sample assessment data structure validated`);
    });
  });

  describe('Service Health Tests', () => {
    it('should check auth service health', async () => {
      try {
        const response = await testHelpers.authClient.get('/health/live');
        expect(response).toHaveProperty('status', 'alive');
        console.log(`✅ Auth service is healthy`);
      } catch (error) {
        console.log(`⚠️ Auth service health check failed: ${error.message}`);
      }
    });

    it('should check assessment service health', async () => {
      try {
        const response = await testHelpers.assessmentClient.get('/health/live');
        expect(response).toHaveProperty('status', 'alive');
        console.log(`✅ Assessment service is healthy`);
      } catch (error) {
        console.log(`⚠️ Assessment service health check failed: ${error.message}`);
      }
    });

    it('should check archive service health', async () => {
      try {
        const response = await testHelpers.archiveClient.get('/health/live');
        expect(response).toHaveProperty('status', 'alive');
        console.log(`✅ Archive service is healthy`);
      } catch (error) {
        console.log(`⚠️ Archive service health check failed: ${error.message}`);
      }
    });
  });
});
