# ATMA Integration Tests

Folder ini berisi integration tests untuk ATMA Backend yang menguji full flow dari register user hing<PERSON> penyimpanan hasil analisis di database melalui microservice archive.

## 📋 Test Coverage

### 🔄 Full Flow Test (`full-flow.test.js` & `full-flow-test.js`)
Test end-to-end lengkap yang mencakup:
1. **User Registration** - Registrasi user baru
2. **User Login** - Login dan mendapatkan JWT token
3. **Assessment Submission** - Submit data assessment (RIASEC, OCEAN, VIA-IS)
4. **Assessment Processing** - Menunggu proses analisis AI selesai
5. **Archive Verification** - Memverifikasi hasil tersimpan di database melalui archive service
6. **Data Integrity** - Validasi struktur dan isi data hasil analisis

### 🧩 Individual Component Tests (`individual-tests.test.js`)
Test untuk komponen individual:
- **Auth Service**: Register, login, error handling
- **Assessment Service**: Submit assessment, validation, authentication
- **Archive Service**: Retrieve results, authentication
- **Data Validation**: Struktur assessment data
- **Service Health**: Health check endpoints

### 🎭 Multiple Assessment Tests (`multiple-assessments.test.js`)
Test dengan berbagai profil assessment:
- **Creative Artist**: High artistic & creativity scores
- **Analytical Researcher**: High investigative & learning scores
- **Social Leader**: High social & leadership scores
- **Practical Organizer**: High conventional & conscientiousness scores
- **Entrepreneurial Innovator**: High enterprising & creativity scores

## 🏗️ Struktur Folder

```
integration-tests/
├── package.json                    # Dependencies dan scripts
├── .env                           # Environment configuration
├── jest.setup.js                  # Jest configuration
├── README.md                      # Dokumentasi ini
├── full-flow.test.js             # Jest full flow test suite
├── full-flow-test.js             # Standalone full flow script
├── individual-tests.test.js      # Individual component tests
├── multiple-assessments.test.js  # Multiple assessment variants test
├── run-tests.js                  # Test runner script
├── run-tests.bat                 # Windows batch runner
├── utils/
│   ├── api-client.js             # HTTP client wrapper
│   └── test-helpers.js           # Test utility functions
└── test-data/
    ├── sample-assessment.json    # Sample assessment data
    └── assessment-variants.json  # Multiple assessment variants
```

## 🚀 Setup

### 1. Install Dependencies

```bash
cd integration-tests
npm install
```

### 2. Configure Environment

Edit file `.env` sesuai dengan konfigurasi services Anda:

```env
# ATMA Backend Services Configuration
AUTH_SERVICE_URL=http://localhost:3001
ASSESSMENT_SERVICE_URL=http://localhost:3003
ARCHIVE_SERVICE_URL=http://localhost:3002
API_GATEWAY_URL=http://localhost:3000

# Test Configuration
TEST_TIMEOUT=30000
WAIT_FOR_PROCESSING=120000

# Test User Credentials
TEST_EMAIL=<EMAIL>
TEST_PASSWORD=password123
```

### 3. Pastikan Services Berjalan

Sebelum menjalankan test, pastikan semua services ATMA Backend sudah berjalan:

```bash
# Dari root directory atma-backend
./start-all-simple.bat
```

Atau jalankan services secara manual:
- Auth Service (Port 3001)
- Assessment Service (Port 3003) 
- Archive Service (Port 3002)
- Analysis Worker
- RabbitMQ
- PostgreSQL

## 🧪 Menjalankan Tests

### Opsi 1: Menggunakan Test Runner (Recommended)

```bash
# Menggunakan Node.js runner
node run-tests.js full-flow      # Full end-to-end test
node run-tests.js individual     # Individual component tests
node run-tests.js jest           # All Jest tests
node run-tests.js health         # Service health check

# Menggunakan batch script (Windows)
run-tests.bat full-flow          # Full end-to-end test
run-tests.bat individual         # Individual component tests
run-tests.bat jest               # All Jest tests
run-tests.bat health             # Service health check
```

### Opsi 2: NPM Scripts

```bash
# Test individual
npm run test:full-flow           # Full end-to-end test
npm run test:individual          # Individual component tests
npm run test:multiple            # Multiple assessment variants
npm run test:health              # Service health check
npm run test:all                 # Run health + full-flow + individual

# Jest commands
npm test                         # All Jest tests
npm run test:watch               # Jest watch mode
npm run test:coverage            # Jest with coverage
```

### Opsi 3: Direct Commands

```bash
# Standalone scripts
node full-flow-test.js           # Full flow test
node run-tests.js health         # Health check

# Jest tests
npx jest full-flow.test.js       # Full flow Jest test
npx jest individual-tests.test.js # Individual tests
npx jest multiple-assessments.test.js # Multiple variants
```

## 📊 Output Test

Test akan menampilkan output detail seperti:

```
🚀 Starting ATMA Full Flow Integration Test
============================================================
📧 Test Email: <EMAIL>
🔗 Auth Service: http://localhost:3001
🔗 Assessment Service: http://localhost:3003
🔗 Archive Service: http://localhost:3002
============================================================

🔸 Step 1: Register User
──────────────────────────────────────────────────────────
🚀 POST http://localhost:3001/auth/register
✅ User registered: <EMAIL> (ID: 123e4567-e89b-12d3-a456-426614174000)
✅ Token Balance: 100

🔸 Step 2: Login User
──────────────────────────────────────────────────────────
🚀 POST http://localhost:3001/auth/login
✅ Login successful, token: eyJhbGciOiJIUzI1NiIs...

🔸 Step 3: Submit Assessment
──────────────────────────────────────────────────────────
🚀 POST http://localhost:3003/assessments/submit
✅ Assessment submitted, Job ID: 456e7890-e89b-12d3-a456-426614174001
✅ Queue position: 1
✅ Estimated processing time: 2-5 minutes

🔸 Step 4: Wait for Assessment Processing
──────────────────────────────────────────────────────────
⏳ Waiting for processing (max 120 seconds)...
📊 Status: queued
⏳ Still processing, waiting 5 seconds...
📊 Status: processing
⏳ Still processing, waiting 5 seconds...
📊 Status: completed
✅ Assessment processing completed!

🔸 Step 5: Verify Results in Archive Service
──────────────────────────────────────────────────────────
⏳ Waiting for archive service to save results...
🚀 GET http://localhost:3002/archive/results
✅ Found 1 results in archive service
✅ Analysis result found: 789e0123-e89b-12d3-a456-426614174002
✅ Status: completed
✅ Created: 2024-01-15T10:30:45.123Z

🔸 Step 6: Verify Data Integrity
──────────────────────────────────────────────────────────
✅ Assessment data integrity verified
✅ Persona profile structure verified
✅ Archetype: The Innovative Analyst
✅ Strengths count: 5
✅ Career suggestions count: 8

🎉 FULL FLOW TEST COMPLETED SUCCESSFULLY!
============================================================
📊 Test Summary:
   👤 User: <EMAIL>
   🆔 User ID: 123e4567-e89b-12d3-a456-426614174000
   🔑 Job ID: 456e7890-e89b-12d3-a456-426614174001
   📋 Result ID: 789e0123-e89b-12d3-a456-426614174002
   🎭 Persona: The Innovative Analyst
   📅 Created: 2024-01-15T10:30:45.123Z
   ⏱️ Total time: 45.6 seconds
============================================================
```

## 🔧 Troubleshooting

### Services Tidak Berjalan
```
❌ Network error: connect ECONNREFUSED 127.0.0.1:3001
```
**Solusi**: Pastikan semua services ATMA Backend sudah berjalan.

### Timeout Processing
```
❌ Assessment processing timeout after 120 seconds
```
**Solusi**: 
- Periksa Analysis Worker berjalan
- Periksa RabbitMQ connection
- Tingkatkan `WAIT_FOR_PROCESSING` di `.env`

### Database Connection Error
```
❌ 500 Failed to retrieve results from archive service
```
**Solusi**: 
- Periksa PostgreSQL berjalan
- Periksa database connection di Archive Service

### Token Balance Insufficient
```
❌ 402 Insufficient token balance
```
**Solusi**: User baru otomatis mendapat 100 tokens. Jika masih error, periksa konfigurasi `ANALYSIS_TOKEN_COST`.

## 📝 Customization

### Menggunakan Assessment Data Custom

Edit file `test-data/sample-assessment.json` atau buat file baru dan update path di test helper.

### Menambah Test Cases

Tambahkan test cases baru di `full-flow.test.js` atau buat file test terpisah.

### Mengubah Timeout

Edit nilai `WAIT_FOR_PROCESSING` di `.env` sesuai kebutuhan (dalam milliseconds).

## 🎯 Expected Results

Test dianggap berhasil jika:

1. ✅ User berhasil register dan login
2. ✅ Assessment berhasil disubmit dan diproses
3. ✅ Hasil analisis tersimpan di database
4. ✅ Data assessment dan persona profile lengkap dan valid
5. ✅ Semua field required ada dan tidak kosong

## 🔍 Monitoring

Untuk monitoring real-time, Anda bisa:

1. **Check RabbitMQ Management**: http://localhost:15672
2. **Check Database**: Query langsung ke PostgreSQL
3. **Check Logs**: Lihat logs di folder `logs/` masing-masing service
4. **Check API Health**: Hit endpoint `/health` di setiap service
